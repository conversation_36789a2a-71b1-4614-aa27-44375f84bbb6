<?php $__env->startSection('title', 'บริการ - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Banner Slider -->
<section class="hero-section position-relative <?php echo e($banners->count() === 0 ? 'hero-fallback' : ''); ?>">
    <?php if($banners->count() > 0): ?>
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <div class="banner-slide" style="background-image: url('<?php echo e(asset('storage/' . $banner->image_path)); ?>');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($banners->count() > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="true" aria-label="Slide <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">บริการของเรา</h1>
                    <p class="lead text-white">บริการจัดงานศพที่ครบครันและเหมาะสมกับทุกความต้องการ</p>
                    <?php if($services->total() > 0): ?>
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-hands-helping me-2"></i>
                            มีบริการทั้งหมด <?php echo e($services->total()); ?> รายการ
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">บริการของเรา</h1>
                    <p class="lead">บริการจัดงานศพที่ครบครันและเหมาะสมกับทุกความต้องการ</p>
                    <?php if($services->total() > 0): ?>
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-hands-helping me-2"></i>
                            มีบริการทั้งหมด <?php echo e($services->total()); ?> รายการ
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Services Section -->
<section class="py-5">
    <div class="container">
        <?php if($services->count() > 0): ?>
        <div class="row g-4">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100 shadow-sm">
                    <div class="card-image-container img-size-large position-relative">
                        <?php
                            $coverImage = $service->images->where('is_cover', true)->first() ?? $service->images->first();
                            $imagePath = $coverImage ? $coverImage->image_path : $service->image;
                        ?>

                        <?php if($imagePath && file_exists(storage_path('app/public/' . $imagePath))): ?>
                        <img src="<?php echo e(asset('storage/' . $imagePath)); ?>"
                             class="img-fit-contain service-image"
                             alt="<?php echo e($service->title); ?>"
                             style="cursor: pointer;"
                             onclick="window.location.href='<?php echo e(route('services.show', $service->id)); ?>'">
                        <?php else: ?>
                        <img src="<?php echo e(asset('images/placeholder.svg')); ?>"
                             class="img-fit-contain service-image"
                             alt="ไม่มีรูปภาพ"
                             style="cursor: pointer;"
                             onclick="window.location.href='<?php echo e(route('services.show', $service->id)); ?>'">
                        <?php endif; ?>

                        <!-- Gallery indicator -->
                        <?php if($service->images->count() > 1): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="fas fa-images me-1"></i><?php echo e($service->images->count()); ?>

                            </span>
                        </div>
                        <?php endif; ?>

                        <!-- Hover overlay -->
                        <div class="card-hover-overlay">
                            <div class="text-center">
                                <i class="fas fa-eye fa-2x text-white mb-2"></i>
                                <p class="text-white mb-0">คลิกเพื่อดูรายละเอียด</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <a href="<?php echo e(route('services.show', $service->id)); ?>" class="text-decoration-none text-dark">
                                <?php echo e($service->title); ?>

                            </a>
                        </h5>
                        <p class="card-text flex-grow-1"><?php echo e($service->description); ?></p>

                        <?php if($service->details): ?>
                        <div class="mb-3">
                            <h6 class="text-muted">รายละเอียดเพิ่มเติม:</h6>
                            <p class="small text-muted"><?php echo e(Str::limit($service->details, 120)); ?></p>
                        </div>
                        <?php endif; ?>

                        <!-- Service features/highlights -->
                        <?php if($service->images->count() > 0): ?>
                        <div class="mb-3">
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>มีแกลเลอรี่รูปภาพ <?php echo e($service->images->count()); ?> รูป
                            </small>
                        </div>
                        <?php endif; ?>

                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('services.show', $service->id)); ?>"
                                   class="btn btn-outline-primary btn-hover-effect">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียดและแกลเลอรี่
                                </a>
                                <a href="<?php echo e(route('contact')); ?>"
                                   class="btn btn-primary btn-hover-effect">
                                    <i class="fas fa-envelope me-2"></i>ติดต่อสอบถาม
                                </a>
                            </div>
                            <small class="text-muted d-block text-center mt-2">
                                <i class="fas fa-phone me-1"></i>สอบถามราคาและรายละเอียดได้ที่เจ้าหน้าที่
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($services->hasPages()): ?>
        <div class="mt-5">
            <?php echo $__env->make('custom.simple-pagination', ['paginator' => $services], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-praying-hands fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีบริการ</h3>
            <p class="text-muted">กรุณาติดต่อเราเพื่อสอบถามบริการจัดงานศพ</p>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Contact CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอดเวลา</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการสแตนบาย ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<style>
/* Service Card Enhancements */
.service-card {
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.service-card .card-body {
    position: relative;
    z-index: 5; /* Ensure card body is above overlay */
}

.service-image {
    transition: transform 0.3s ease;
}

.service-card:hover .service-image {
    transform: scale(1.05);
}

.card-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none; /* Allow clicks to pass through */
}

.service-card:hover .card-hover-overlay {
    opacity: 1;
}

.btn-hover-effect {
    transition: all 0.3s ease;
    position: relative;
    z-index: 10; /* Ensure buttons are above overlay */
}

.btn-hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-title a:hover {
    color: #2c3e50 !important;
}

/* Gallery badge animation */
.badge {
    transition: all 0.3s ease;
}

.service-card:hover .badge {
    transform: scale(1.1);
}

/* Loading animation for images */
.service-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.service-image[src] {
    background: none;
    animation: none;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .service-card:hover {
        transform: none;
    }

    .card-hover-overlay {
        display: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading state to images
    const images = document.querySelectorAll('.service-image');

    images.forEach(img => {
        img.addEventListener('load', function() {
            this.classList.add('loaded');
        });

        img.addEventListener('error', function() {
            this.src = '<?php echo e(asset("images/placeholder.svg")); ?>';
            this.alt = 'ไม่สามารถโหลดรูปภาพได้';
        });
    });

    // Service card click functionality
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on the button
            if (e.target.closest('.btn')) {
                return;
            }

            const link = card.querySelector('a[href*="services"]');
            if (link) {
                window.location.href = link.href;
            }
        });
    });

    // Smooth scroll for pagination
    const paginationLinks = document.querySelectorAll('.pagination a');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Smooth scroll to top of services section
            setTimeout(() => {
                document.querySelector('.hero-section').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/services.blade.php ENDPATH**/ ?>