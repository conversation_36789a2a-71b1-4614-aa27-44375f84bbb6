@extends('layouts.admin')

@section('title', 'แดชบอร์ด - Phuyai Prajak Service Shop Admin')

@section('content')
<!-- Welcome Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">สวัสดี, {{ Auth::user()->name }}! 👋</h1>
                <p class="text-muted mb-0">ยินดีต้อนรับสู่ระบบจัดการ Phuyai Prajak Service Shop</p>
            </div>
            <div class="text-end">
                <small class="text-muted">วันที่: {{ now()->format('d/m/Y H:i') }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-6 col-lg-3">
        <a href="{{ route('admin.services') }}" class="text-decoration-none">
            <div class="card stats-card pulse-hover shadow-primary">
                <div class="card-body text-white position-relative">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 opacity-90">บริการทั้งหมด</h6>
                            <h2 class="mb-0 fw-bold">{{ $stats['services_count'] }}</h2>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-cogs fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3">
        <a href="{{ route('admin.packages') }}" class="text-decoration-none">
            <div class="card bg-success text-white pulse-hover shadow-success">
                <div class="card-body position-relative">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 opacity-90">แพคเกจทั้งหมด</h6>
                            <h2 class="mb-0 fw-bold">{{ $stats['packages_count'] }}</h2>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3">
        <a href="{{ route('admin.activities') }}" class="text-decoration-none">
            <div class="card bg-warning text-white pulse-hover shadow-warning">
                <div class="card-body position-relative">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 opacity-90">ผลงานทั้งหมด</h6>
                            <h2 class="mb-0 fw-bold">{{ $stats['activities_count'] }}</h2>
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-images fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>

    <div class="col-md-6 col-lg-3">
        <a href="{{ route('admin.contacts') }}" class="text-decoration-none">
            <div class="card bg-info text-white pulse-hover shadow-primary">
                <div class="card-body position-relative">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 opacity-90">ข้อความติดต่อ</h6>
                            <h2 class="mb-0 fw-bold">{{ $stats['contacts_count'] }}</h2>
                            @if($stats['unread_contacts'] > 0)
                            <small class="opacity-90">{{ $stats['unread_contacts'] }} ข้อความใหม่</small>
                            @endif
                        </div>
                        <div class="text-white-50">
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>
</div>

<!-- Quick Actions -->
<div class="row g-4 mb-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>การดำเนินการด่วน
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="{{ route('admin.services.create') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('admin.packages.create') }}" class="btn btn-success w-100">
                            <i class="fas fa-plus me-2"></i>เพิ่มแพคเกจใหม่
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('admin.activities.create') }}" class="btn btn-warning w-100">
                            <i class="fas fa-plus me-2"></i>เพิ่มผลงานใหม่
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('admin.settings') }}" class="btn btn-info w-100">
                            <i class="fas fa-cog me-2"></i>ตั้งค่าเว็บไซต์
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Contacts -->
<div class="row g-4 mb-5">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-envelope me-2 text-info"></i>ข้อความติดต่อล่าสุด (3 รายการ)
                </h5>
                <a href="{{ route('admin.contacts') }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-list me-1"></i>ดูทั้งหมด
                </a>
            </div>
            <div class="card-body">
                @if($recent_contacts->count() > 0)
                <div class="list-group list-group-flush">
                    @foreach($recent_contacts as $contact)
                    <div class="list-group-item px-0 py-3 border-0 {{ !$contact->is_read ? 'bg-warning bg-opacity-10' : '' }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-2">{{ $contact->name }}</h6>
                                    @if(!$contact->is_read)
                                    <span class="badge bg-warning text-dark">ใหม่</span>
                                    @else
                                    <span class="badge bg-success">อ่านแล้ว</span>
                                    @endif
                                </div>
                                <p class="mb-1 fw-medium">{{ Str::limit($contact->subject, 60) }}</p>
                                <p class="mb-2 text-muted small">{{ Str::limit($contact->message, 80) }}</p>
                                <div class="d-flex align-items-center text-muted small">
                                    <i class="fas fa-envelope me-1"></i>{{ $contact->email }}
                                    @if($contact->phone)
                                    <span class="ms-3"><i class="fas fa-phone me-1"></i>{{ $contact->phone }}</span>
                                    @endif
                                    <span class="ms-3"><i class="fas fa-clock me-1"></i>{{ $contact->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                            <div class="ms-3">
                                <a href="{{ route('admin.contacts.show', $contact->id) }}"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>ดู
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center text-muted py-5">
                    <i class="fas fa-inbox fa-4x mb-3 opacity-50"></i>
                    <h5>ยังไม่มีข้อความติดต่อ</h5>
                    <p class="mb-0">เมื่อมีลูกค้าติดต่อเข้ามา ข้อความจะแสดงที่นี่</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto refresh unread contacts count every 30 seconds
    setInterval(function() {
        fetch('{{ route("admin.dashboard") }}', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            // Update only the unread contacts badge if needed
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newUnreadCount = doc.querySelector('.badge.bg-danger');
            const currentUnreadBadge = document.querySelector('.badge.bg-danger');

            if (newUnreadCount && currentUnreadBadge) {
                currentUnreadBadge.textContent = newUnreadCount.textContent;
            }
        })
        .catch(error => {
            console.log('Auto refresh failed:', error);
        });
    }, 30000); // 30 seconds
});
</script>
@endsection
